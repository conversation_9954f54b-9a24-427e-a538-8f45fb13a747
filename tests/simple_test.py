#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     simple_test.py
@Date:      2025/7/30
@Author:    AI Assistant
@Desc:      简化的测试脚本，验证 get_alert_panel 接口的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_policy_name_parsing():
    """测试 Policy Name 解析逻辑"""
    print("🔍 测试 Policy Name 解析逻辑...")
    
    def parse_policy_name(policy_name):
        """解析policy_name，支持3-5段式命名规范"""
        # 检查空值
        if not policy_name or policy_name in ('', 'null', 'NULL'):
            return None, None, None, None, None
        
        # 检查是否包含分隔符
        if '-' not in policy_name:
            return None, None, None, None, None
        
        parts = policy_name.split('-')
        
        # 验证至少包含3个段
        if len(parts) < 3:
            return None, None, None, None, None
        
        # 解析各个段，支持3-5段式
        product = parts[0].strip() if len(parts) > 0 else None
        business_line = parts[1].strip() if len(parts) > 1 else None
        criticality = parts[2].strip() if len(parts) > 2 else None
        severity = parts[3].strip() if len(parts) > 3 else None
        purpose = parts[4].strip() if len(parts) > 4 else None
        
        # 验证必要字段不为空
        if not product or not business_line or not criticality:
            return None, None, None, None, None
        
        return product, business_line, criticality, severity, purpose
    
    test_cases = [
        # 有效格式测试
        {
            'input': 'cvm-compute-core',
            'expected': ('cvm', 'compute', 'core', None, None),
            'description': '3段式格式'
        },
        {
            'input': 'rds-database-core-critical',
            'expected': ('rds', 'database', 'core', 'critical', None),
            'description': '4段式格式'
        },
        {
            'input': 'cdn-network-noncore-warning-monitoring',
            'expected': ('cdn', 'network', 'noncore', 'warning', 'monitoring'),
            'description': '5段式格式'
        },
        # 无效格式测试
        {
            'input': '',
            'expected': (None, None, None, None, None),
            'description': '空字符串'
        },
        {
            'input': 'cvm-compute',
            'expected': (None, None, None, None, None),
            'description': '段数不足'
        },
        {
            'input': 'cvmcompute',
            'expected': (None, None, None, None, None),
            'description': '缺少分隔符'
        }
    ]
    
    passed = 0
    failed = 0
    
    for case in test_cases:
        try:
            result = parse_policy_name(case['input'])
            if result == case['expected']:
                print(f"  ✅ {case['description']}: {case['input']} -> {result}")
                passed += 1
            else:
                print(f"  ❌ {case['description']}: {case['input']}")
                print(f"     期望: {case['expected']}")
                print(f"     实际: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ {case['description']}: {case['input']} - 异常: {e}")
            failed += 1
    
    return passed, failed


def test_status_mapping():
    """测试状态映射逻辑"""
    print("\n🔍 测试状态映射逻辑...")
    
    def map_status_to_health(status, severity):
        """将数据库状态映射为前端健康状态"""
        if status == 'OK':
            return 'healthy'
        elif status == 'ALARM':
            if severity == 'critical':
                return 'critical'
            elif severity == 'warning':
                return 'warning'
            else:
                return 'error'
        elif status == 'NO_DATA':
            return 'info'
        elif status == 'NO_CONF':
            return 'warning'
        else:
            return 'info'
    
    test_cases = [
        # OK状态测试
        {
            'status': 'OK',
            'severity': 'critical',
            'expected': 'healthy',
            'description': 'OK状态 -> healthy'
        },
        # ALARM状态测试
        {
            'status': 'ALARM',
            'severity': 'critical',
            'expected': 'critical',
            'description': 'ALARM + critical -> critical'
        },
        {
            'status': 'ALARM',
            'severity': 'warning',
            'expected': 'warning',
            'description': 'ALARM + warning -> warning'
        },
        {
            'status': 'ALARM',
            'severity': 'info',
            'expected': 'error',
            'description': 'ALARM + info -> error'
        },
        # 其他状态测试
        {
            'status': 'NO_DATA',
            'severity': 'any',
            'expected': 'info',
            'description': 'NO_DATA -> info'
        },
        {
            'status': 'NO_CONF',
            'severity': 'any',
            'expected': 'warning',
            'description': 'NO_CONF -> warning'
        },
        {
            'status': 'UNKNOWN',
            'severity': 'any',
            'expected': 'info',
            'description': 'UNKNOWN -> info'
        }
    ]
    
    passed = 0
    failed = 0
    
    for case in test_cases:
        try:
            result = map_status_to_health(case['status'], case['severity'])
            if result == case['expected']:
                print(f"  ✅ {case['description']}")
                passed += 1
            else:
                print(f"  ❌ {case['description']}")
                print(f"     期望: {case['expected']}")
                print(f"     实际: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ {case['description']} - 异常: {e}")
            failed += 1
    
    return passed, failed


def test_response_structure():
    """测试响应数据结构"""
    print("\n🔍 测试响应数据结构...")
    
    # 模拟一个标准的响应数据结构
    sample_response = {
        'status': 'success',
        'message': '获取数据成功',
        'data': {
            'items': [
                {
                    'id': 'category_计算',
                    'productName': '',
                    'category': '计算',
                    'isCategory': True,
                    'rss': False,
                    'current': {
                        'statuses': [],
                        'hasData': False
                    }
                },
                {
                    'id': '计算_0',
                    'productName': 'cvm',
                    'category': '计算',
                    'isCategory': False,
                    'rss': True,
                    'current': {
                        'statuses': ['critical'],
                        'hasData': True,
                        'timestamp': '2025-07-30 10:00:00'
                    },
                    'date_2025_07_30': {
                        'statuses': ['critical'],
                        'hasData': True,
                        'timestamp': '2025-07-30 10:00:00'
                    }
                }
            ],
            'statistics': {
                'healthy': 0,
                'info': 0,
                'warning': 0,
                'error': 0,
                'critical': 1,
                'total': 1
            },
            'dateRange': [
                '2025-07-30',
                '2025-07-29',
                '2025-07-28',
                '2025-07-27',
                '2025-07-26',
                '2025-07-25',
                '2025-07-24'
            ]
        }
    }
    
    test_cases = [
        {
            'check': lambda r: 'status' in r,
            'description': '包含 status 字段'
        },
        {
            'check': lambda r: 'message' in r,
            'description': '包含 message 字段'
        },
        {
            'check': lambda r: 'data' in r,
            'description': '包含 data 字段'
        },
        {
            'check': lambda r: 'items' in r['data'],
            'description': 'data 包含 items 字段'
        },
        {
            'check': lambda r: 'statistics' in r['data'],
            'description': 'data 包含 statistics 字段'
        },
        {
            'check': lambda r: 'dateRange' in r['data'],
            'description': 'data 包含 dateRange 字段'
        },
        {
            'check': lambda r: isinstance(r['data']['items'], list),
            'description': 'items 是列表类型'
        },
        {
            'check': lambda r: isinstance(r['data']['statistics'], dict),
            'description': 'statistics 是字典类型'
        },
        {
            'check': lambda r: len(r['data']['dateRange']) == 7,
            'description': 'dateRange 包含7个日期'
        },
        {
            'check': lambda r: all(key in r['data']['statistics'] for key in ['healthy', 'info', 'warning', 'error', 'critical', 'total']),
            'description': 'statistics 包含所有必需的健康状态字段'
        }
    ]
    
    passed = 0
    failed = 0
    
    for case in test_cases:
        try:
            if case['check'](sample_response):
                print(f"  ✅ {case['description']}")
                passed += 1
            else:
                print(f"  ❌ {case['description']}")
                failed += 1
        except Exception as e:
            print(f"  ❌ {case['description']} - 异常: {e}")
            failed += 1
    
    return passed, failed


def test_data_aggregation():
    """测试数据聚合逻辑"""
    print("\n🔍 测试数据聚合逻辑...")
    
    # 模拟告警数据
    mock_alerts = [
        {
            'id': 1,
            'status': 'ALARM',
            'severity': 'critical',
            'labels': {'policy_name': 'cvm-compute-core'},
            'starts_at': '2025-07-30 10:00:00'
        },
        {
            'id': 2,
            'status': 'OK',
            'severity': 'warning',
            'labels': {'policy_name': 'cvm-compute-core'},
            'starts_at': '2025-07-30 11:00:00'
        },
        {
            'id': 3,
            'status': 'NO_DATA',
            'severity': 'info',
            'labels': {'policy_name': 'rds-database-core'},
            'starts_at': '2025-07-30 12:00:00'
        }
    ]
    
    # 模拟产品分类映射
    product_category_map = {
        'cvm': '计算',
        'rds': '数据库'
    }
    
    def parse_policy_name(policy_name):
        if not policy_name or '-' not in policy_name:
            return None, None, None, None, None
        parts = policy_name.split('-')
        if len(parts) < 3:
            return None, None, None, None, None
        return parts[0], parts[1], parts[2], None, None
    
    def map_status_to_health(status, severity):
        if status == 'OK':
            return 'healthy'
        elif status == 'ALARM':
            return 'critical' if severity == 'critical' else 'warning' if severity == 'warning' else 'error'
        elif status == 'NO_DATA':
            return 'info'
        elif status == 'NO_CONF':
            return 'warning'
        else:
            return 'info'
    
    # 执行聚合逻辑
    category_stats = {}
    total_statistics = {
        'healthy': 0,
        'info': 0,
        'warning': 0,
        'error': 0,
        'critical': 0,
        'total': 0
    }
    
    for alert in mock_alerts:
        policy_name = alert['labels'].get('policy_name')
        if not policy_name:
            continue
            
        product, business_line, criticality, _, _ = parse_policy_name(policy_name)
        if not product or not business_line or not criticality:
            continue
        
        product_category = product_category_map.get(product, business_line)
        health_status = map_status_to_health(alert['status'], alert['severity'])
        
        # 统计分类数据
        if product_category not in category_stats:
            category_stats[product_category] = {
                'healthy': 0, 'info': 0, 'warning': 0, 'error': 0, 'critical': 0, 'total': 0
            }
        
        category_stats[product_category][health_status] += 1
        category_stats[product_category]['total'] += 1
        
        # 更新总统计
        total_statistics[health_status] += 1
        total_statistics['total'] += 1
    
    test_cases = [
        {
            'check': lambda: len(category_stats) == 2,
            'description': '正确识别2个产品分类'
        },
        {
            'check': lambda: '计算' in category_stats,
            'description': '包含计算分类'
        },
        {
            'check': lambda: '数据库' in category_stats,
            'description': '包含数据库分类'
        },
        {
            'check': lambda: category_stats['计算']['critical'] == 1,
            'description': '计算分类包含1个critical告警'
        },
        {
            'check': lambda: category_stats['计算']['healthy'] == 1,
            'description': '计算分类包含1个healthy告警'
        },
        {
            'check': lambda: category_stats['数据库']['info'] == 1,
            'description': '数据库分类包含1个info告警'
        },
        {
            'check': lambda: total_statistics['total'] == 3,
            'description': '总计3个告警'
        },
        {
            'check': lambda: total_statistics['critical'] == 1,
            'description': '总计1个critical告警'
        },
        {
            'check': lambda: total_statistics['healthy'] == 1,
            'description': '总计1个healthy告警'
        },
        {
            'check': lambda: total_statistics['info'] == 1,
            'description': '总计1个info告警'
        }
    ]
    
    passed = 0
    failed = 0
    
    for case in test_cases:
        try:
            if case['check']():
                print(f"  ✅ {case['description']}")
                passed += 1
            else:
                print(f"  ❌ {case['description']}")
                failed += 1
        except Exception as e:
            print(f"  ❌ {case['description']} - 异常: {e}")
            failed += 1
    
    return passed, failed


def main():
    """主测试函数"""
    print("🚀 开始运行 get_alert_panel 接口核心功能测试")
    print("=" * 60)
    
    total_passed = 0
    total_failed = 0
    
    # 运行各项测试
    tests = [
        test_policy_name_parsing,
        test_status_mapping,
        test_response_structure,
        test_data_aggregation
    ]
    
    for test_func in tests:
        try:
            passed, failed = test_func()
            total_passed += passed
            total_failed += failed
        except Exception as e:
            print(f"❌ 测试函数 {test_func.__name__} 执行失败: {e}")
            total_failed += 1
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print(f"✅ 通过: {total_passed}")
    print(f"❌ 失败: {total_failed}")
    print(f"📈 成功率: {total_passed/(total_passed+total_failed)*100:.1f}%" if (total_passed+total_failed) > 0 else "0.0%")
    
    if total_failed == 0:
        print("\n🎉 所有测试通过！get_alert_panel 接口核心功能正常。")
        return 0
    else:
        print(f"\n⚠️  有 {total_failed} 个测试失败，请检查上述错误信息。")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
