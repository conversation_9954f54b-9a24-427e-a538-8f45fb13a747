#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     conftest.py
@Date:      2025/7/30
@Author:    AI Assistant
@Desc:      pytest配置文件和共享fixtures
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask

# 尝试导入模型，如果失败则创建Mock
try:
    from model import MonitoringCenterAlert, TencentCateProduct
except ImportError:
    # 如果导入失败，创建Mock类
    class MonitoringCenterAlert:
        pass

    class TencentCateProduct:
        pass


@pytest.fixture
def app():
    """创建测试用的Flask应用"""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    # 注册蓝图
    from blueprint.moniterCenter.alerts_center import alerts_center_bp
    app.register_blueprint(alerts_center_bp)
    
    return app


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def mock_db_session():
    """模拟数据库会话"""
    with patch('model.db.session') as mock_session:
        yield mock_session


@pytest.fixture
def sample_alerts():
    """创建示例告警数据"""
    alerts = []
    
    # 正常的5段式policy_name
    alert1 = Mock(spec=MonitoringCenterAlert)
    alert1.id = 1
    alert1.status = 'ALARM'
    alert1.severity = 'critical'
    alert1.labels = {'policy_name': 'cvm-compute-core-critical-monitoring'}
    alert1.starts_at = datetime.now() - timedelta(hours=1)
    alert1.summary = 'CVM CPU使用率过高'
    alerts.append(alert1)
    
    # 4段式policy_name
    alert2 = Mock(spec=MonitoringCenterAlert)
    alert2.id = 2
    alert2.status = 'OK'
    alert2.severity = 'warning'
    alert2.labels = {'policy_name': 'rds-database-core-warning'}
    alert2.starts_at = datetime.now() - timedelta(hours=2)
    alert2.summary = 'RDS连接数恢复正常'
    alerts.append(alert2)
    
    # 3段式policy_name
    alert3 = Mock(spec=MonitoringCenterAlert)
    alert3.id = 3
    alert3.status = 'NO_DATA'
    alert3.severity = 'info'
    alert3.labels = {'policy_name': 'cdn-network-non-core'}
    alert3.starts_at = datetime.now() - timedelta(hours=3)
    alert3.summary = 'CDN监控数据不足'
    alerts.append(alert3)
    
    # 格式错误的policy_name（段数不足）
    alert4 = Mock(spec=MonitoringCenterAlert)
    alert4.id = 4
    alert4.status = 'ALARM'
    alert4.severity = 'warning'
    alert4.labels = {'policy_name': 'invalid-format'}
    alert4.starts_at = datetime.now() - timedelta(hours=4)
    alert4.summary = '格式错误的告警'
    alerts.append(alert4)
    
    # 空labels
    alert5 = Mock(spec=MonitoringCenterAlert)
    alert5.id = 5
    alert5.status = 'NO_CONF'
    alert5.severity = 'warning'
    alert5.labels = None
    alert5.starts_at = datetime.now() - timedelta(hours=5)
    alert5.summary = '空labels的告警'
    alerts.append(alert5)
    
    # labels中没有policy_name
    alert6 = Mock(spec=MonitoringCenterAlert)
    alert6.id = 6
    alert6.status = 'ALARM'
    alert6.severity = 'critical'
    alert6.labels = {'other_field': 'value'}
    alert6.starts_at = datetime.now() - timedelta(hours=6)
    alert6.summary = '缺少policy_name的告警'
    alerts.append(alert6)
    
    return alerts


@pytest.fixture
def sample_category_products():
    """创建示例产品分类数据"""
    products = []
    
    product1 = Mock(spec=TencentCateProduct)
    product1.id = 1
    product1.product = 'cvm'
    product1.categories = '计算'
    products.append(product1)
    
    product2 = Mock(spec=TencentCateProduct)
    product2.id = 2
    product2.product = 'rds'
    product2.categories = '数据库'
    products.append(product2)
    
    product3 = Mock(spec=TencentCateProduct)
    product3.id = 3
    product3.product = 'cdn'
    product3.categories = '网络'
    products.append(product3)
    
    return products


@pytest.fixture
def mock_current_app():
    """模拟current_app"""
    with patch('blueprint.moniterCenter.alerts_center.current_app') as mock_app:
        mock_app.logger = Mock()
        mock_app.logger.info = Mock()
        mock_app.logger.warning = Mock()
        mock_app.logger.error = Mock()
        mock_app.logger.debug = Mock()
        yield mock_app


@pytest.fixture
def mock_format_datetime():
    """模拟format_datetime函数"""
    with patch('blueprint.moniterCenter.alerts_center.format_datetime') as mock_func:
        mock_func.side_effect = lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None
        yield mock_func
