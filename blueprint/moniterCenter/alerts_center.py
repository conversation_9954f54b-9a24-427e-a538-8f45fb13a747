# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     alerts_center.py
@Date:      2025/4/3 16:47
@Author:    wanglh
@Desc:      告警中心API接口
"""

from datetime import datetime, timedelta

from flask import Blueprint, current_app, jsonify, request
from sqlalchemy import case, func

from model import MonitoringCenterAlert, TencentProductList, TencentCateProduct, db

alerts_center_bp = Blueprint(
    'alerts_center_api', __name__, url_prefix='/alertscenter/api/v1')


def format_datetime(dt):
    return dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None


def parse_datetime(date_str):
    """解析多种格式的日期字符串为datetime对象

    Args:
        date_str: 日期时间字符串

    Returns:
        datetime: 解析后的datetime对象

    Raises:
        ValueError: 如果日期格式不支持或无效
    """
    if not date_str:
        raise ValueError("日期字符串不能为空")

    if 'Z' in date_str:
        date_str = date_str.replace('Z', '')

    if '.' in date_str:
        date_str = date_str.split('.')[0]

    formats = [
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d"
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    raise ValueError(
        "不支持的日期格式。支持的格式：YYYY-MM-DDTHH:MM:SS[.fff]Z, YYYY-MM-DD HH:MM:SS, YYYY-MM-DD"
    )


@alerts_center_bp.route('/sources', methods=['GET'])
def get_alert_sources():
    """获取告警来源统计"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        status = request.args.get('status', '')
        current_app.logger.info(f"获取告警来源统计: {start_time} - {end_time}")

        if not start_time or not end_time:
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_time = today.strftime("%Y-%m-%d %H:%M:%S")
            end_time = (today + timedelta(days=1, seconds=-1)
                        ).strftime("%Y-%m-%d %H:%M:%S")

        latest_alerts_subquery = db.session.query(
            MonitoringCenterAlert.fingerprint,
            func.max(MonitoringCenterAlert.starts_at).label('max_start_time')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.fingerprint).subquery()

        source_stats = db.session.query(
            MonitoringCenterAlert.source,
            func.count(MonitoringCenterAlert.id).label('count')
        ).join(
            latest_alerts_subquery,
            db.and_(
                MonitoringCenterAlert.fingerprint == latest_alerts_subquery.c.fingerprint,
                MonitoringCenterAlert.starts_at == latest_alerts_subquery.c.max_start_time
            )
        )

        if status == "true":
            source_stats = source_stats.filter(
                MonitoringCenterAlert.status != 'OK'
            ).group_by(
                MonitoringCenterAlert.source
            ).all()
        else:
            source_stats = source_stats.group_by(
                MonitoringCenterAlert.source
            ).all()

        sources = [
            {
                'source': source,
                'count': count
            }
            for source, count in source_stats
        ]

        return jsonify({
            'status': 'success',
            'data': {
                'sources': sources
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取告警来源统计失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '服务器内部错误'
        }), 500


@alerts_center_bp.route('/active', methods=['GET'])
def list_active_alerts():
    """获取实时告警状态"""
    try:

        severity = request.args.get('severity')
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        page = int(request.args.get('page', 1))
        source = request.args.get('source')
        status = request.args.get('status')
        per_page = int(request.args.get('per_page', 20))

        if not end_time:
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if not start_time:
            start_time = (datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S") -
                          timedelta(hours=6)).strftime("%Y-%m-%d %H:%M:%S")

        base_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time,
            MonitoringCenterAlert.severity != 'resolved',
            MonitoringCenterAlert.status != 'OK',
            MonitoringCenterAlert.ends_at.is_(None)
        )

        if severity:
            base_query = base_query.filter(
                MonitoringCenterAlert.severity == severity)
        if status:
            base_query = base_query.filter(
                MonitoringCenterAlert.status == status)
        if source:
            base_query = base_query.filter(
                MonitoringCenterAlert.source == source)

        total_alerts = base_query.count()

        max_page = (total_alerts + per_page -
                    1) // per_page if total_alerts > 0 else 1

        if page > max_page:
            return jsonify({
                'status': 'error',
                'message': f'页码超出范围。最大页码为: {max_page}，当前请求页码为: {page}'
            }), 400

        alerts = base_query.order_by(MonitoringCenterAlert.updated_at.desc()) \
            .offset((page - 1) * per_page) \
            .limit(per_page) \
            .all()

        severity_stats = db.session.query(
            MonitoringCenterAlert.severity,
            func.count(MonitoringCenterAlert.id)
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time,
            MonitoringCenterAlert.severity != 'resolved',
            MonitoringCenterAlert.status != 'OK',
            MonitoringCenterAlert.ends_at.is_(None)
        ).group_by(MonitoringCenterAlert.severity).all()

        results = []
        for alert in alerts:
            results.append({
                'id': alert.id,
                'source': alert.source,
                'fingerprint': alert.fingerprint,
                'external_id': alert.external_id,
                'status': alert.status,
                'severity': alert.severity,
                'summary': alert.summary,
                'description': alert.description,
                'labels': alert.labels,
                'annotations': alert.annotations,
                'starts_at': format_datetime(alert.starts_at),
                'ends_at': format_datetime(alert.ends_at),
                'created_at': format_datetime(alert.created_at),
                'updated_at': format_datetime(alert.updated_at),
                'acknowledged_by': alert.acknowledged_by,
                'acknowledged_at': format_datetime(alert.acknowledged_at),
                'alarm_status': alert.alarm_status,
                'account_id': alert.account_id
            })

        return jsonify({
            'status': 'success',
            'data': {
                'alerts': results,
                'total': total_alerts,
                'statistics': {
                    'severity_stats': {severity: count for severity, count in severity_stats}
                },
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_pages': max_page,
                    'total_records': total_alerts
                }
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取实时告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取实时告警失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/history', methods=['GET'])
def list_history_alerts():
    """获取历史告警数据"""
    try:

        status = request.args.get('status')
        severity = request.args.get('severity')
        source = request.args.get('source')
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400

        base_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        )

        total_all = base_query.count()

        status_stats = db.session.query(
            MonitoringCenterAlert.status,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.status).all()

        status_counts = {
            status: count
            for status, count in status_stats
        }

        severity_stats = db.session.query(
            MonitoringCenterAlert.severity,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.severity).all()

        severity_counts = {
            severity: count
            for severity, count in severity_stats if severity != "resolved"
        }

        source_stats = db.session.query(
            MonitoringCenterAlert.source,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.source).all()

        source_counts = {
            source: count
            for source, count in source_stats
        }

        if status:
            base_query = base_query.filter(
                MonitoringCenterAlert.status == status)
        if severity:
            base_query = base_query.filter(
                MonitoringCenterAlert.severity == severity)
        if source:
            base_query = base_query.filter(
                MonitoringCenterAlert.source == source)

        total_filter = base_query.count()

        max_page = (total_filter + per_page -
                    1) // per_page if total_filter > 0 else 1

        if page > max_page:
            return jsonify({
                'status': 'error',
                'message': f'页码超出范围。最大页码为: {max_page}，当前请求页码为: {page}'
            }), 400

        alerts = base_query.order_by(MonitoringCenterAlert.updated_at.desc()) \
            .offset((page - 1) * per_page) \
            .limit(per_page) \
            .all()

        results = []
        for alert in alerts:
            results.append({
                'id': alert.id,
                'source': alert.source,
                'fingerprint': alert.fingerprint,
                'external_id': alert.external_id,
                'status': alert.status,
                'severity': alert.severity,
                'summary': alert.summary,
                'description': alert.description,
                'labels': alert.labels,
                'annotations': alert.annotations,
                'starts_at': format_datetime(alert.starts_at),
                'ends_at': format_datetime(alert.ends_at),
                'created_at': format_datetime(alert.created_at),
                'updated_at': format_datetime(alert.updated_at),
                'acknowledged_by': alert.acknowledged_by,
                'acknowledged_at': format_datetime(alert.acknowledged_at),
                'alarm_status': alert.alarm_status,
                'account_id': alert.account_id
            })

        return jsonify({
            'status': 'success',
            'data': {
                'alerts': results,
                'total': total_filter,
                'statistics': {
                    'status_stats': status_counts,
                    'source_stats': source_counts,
                    'severity_stats': severity_counts
                },
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_pages': max_page,
                    'total_records': total_filter
                }
            },
            'total_all': total_all
        })

    except ValueError as e:
        current_app.logger.error(f"参数错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"参数错误: {str(e)}"
        }), 400
    except Exception as e:
        current_app.logger.error(f"获取历史告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取历史告警失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/trend', methods=['GET'])
def get_alert_trend():
    """获取告警趋势分析数据"""
    try:

        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400

        try:
            start_time = parse_datetime(start_time)
            end_time = parse_datetime(end_time)
        except ValueError as e:
            return jsonify({
                'status': 'error',
                'message': f'时间格式错误: {str(e)}'
            }), 400

        time_diff = (end_time - start_time).total_seconds() / 86400

        if time_diff <= 3:

            interval = 'hour'
            time_format = '%Y-%m-%d %H:00:00'
            date_trunc = func.date_format(
                MonitoringCenterAlert.starts_at, time_format)

            time_points = []
            current = start_time.replace(minute=0, second=0, microsecond=0)
            while current <= end_time:
                time_points.append(current.strftime(time_format))
                current += timedelta(hours=1)
        else:

            interval = 'day'
            time_format = '%Y-%m-%d'
            date_trunc = func.date_format(
                MonitoringCenterAlert.starts_at, time_format)

            time_points = []
            current = start_time.replace(
                hour=0, minute=0, second=0, microsecond=0)
            while current <= end_time:
                time_points.append(current.strftime(time_format))
                current += timedelta(days=1)

        results = db.session.query(
            date_trunc.label('time_bucket'),
            func.count(MonitoringCenterAlert.id).label('total_count'),
            func.sum(case([(MonitoringCenterAlert.severity == 'critical', 1)], else_=0)).label(
                'critical_count'),
            func.sum(case([(MonitoringCenterAlert.severity == 'warning', 1)], else_=0)).label(
                'warning_count'),
            func.sum(case([(MonitoringCenterAlert.status == 'OK', 1)], else_=0)).label(
                'resolved_count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by('time_bucket').all()

        result_dict = {
            row.time_bucket: {
                'total': int(row.total_count or 0),
                'critical': int(row.critical_count or 0),
                'warning': int(row.warning_count or 0),
                'resolved': int(row.resolved_count or 0)
            } for row in results
        }

        trend_data = []
        for time_point in time_points:
            if time_point in result_dict:
                data = result_dict[time_point]
            else:
                data = {
                    'total': 0,
                    'critical': 0,
                    'warning': 0,
                    'resolved': 0
                }
            trend_data.append({
                'timestamp': time_point,
                **data
            })

        summary = {
            'total': sum(item['total'] for item in trend_data),
            'critical': sum(item['critical'] for item in trend_data),
            'warning': sum(item['warning'] for item in trend_data),
            'resolved': sum(item['resolved'] for item in trend_data)
        }

        return jsonify({
            'status': 'success',
            'data': {
                'interval': interval,
                'trend': trend_data,
                'summary': summary
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取告警趋势数据失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取告警趋势数据失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/<fingerprint>/over', methods=['POST'])
def recover_alert(fingerprint: str = None):
    """设置告警结束"""
    try:
        if not fingerprint:
            return jsonify({
                'status': 'error',
                'message': '缺少必要的fingerprint参数'
            }), 400

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        alert = MonitoringCenterAlert.query.filter_by(
            fingerprint=fingerprint,
            status='ALARM'
        ).first()

        if not alert:
            return jsonify({
                'status': 'error',
                'message': f'未找到未恢复的告警记录: {fingerprint}'
            }), 404

        alert.status = 'OK'
        alert.ends_at = current_time
        alert.updated_at = current_time

        try:
            db.session.commit()
            return jsonify({
                'status': 'success',
                'data': {
                    'id': alert.id,
                    'fingerprint': alert.fingerprint,
                    'status': alert.status,
                    'ends_at': alert.ends_at,
                    'updated_at': alert.updated_at
                }
            })
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新告警状态失败: {str(e)}")
            raise

    except Exception as e:
        current_app.logger.error(f"恢复告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"恢复告警失败: {str(e)}"
        }), 500
    finally:
        db.session.close()


def aggregate_items(items):
    """聚合相同name的告警项"""
    aggregated = {}
    for item in items:
        name = item['name']
        if name not in aggregated:
            aggregated[name] = {
                'name': name,
                'severities': [],
                'critical_count': 0,
                'warning_count': 0,
                'info_count': 0,
                'account_id': item['account_id'],
            }

        for severity in item['severities']:
            if severity not in aggregated[name]['severities']:
                aggregated[name]['severities'].append(severity)

        aggregated[name]['critical_count'] += item['critical_count']
        aggregated[name]['warning_count'] += item['warning_count']
        aggregated[name]['info_count'] += item['info_count']

    return list(aggregated.values())


@alerts_center_bp.route('/healthboard', methods=['GET'])
def get_healthboard_data():
    try:

        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        base_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        )
        statistics_query = base_query.with_entities(
            MonitoringCenterAlert.status,
            func.count(MonitoringCenterAlert.id).label('count')
        ).group_by(MonitoringCenterAlert.status)

        statistics = {status: count for status,
                      count in statistics_query.all()}

        alerts = base_query.filter(
            MonitoringCenterAlert.status == 'ALARM').all()

        items = {}

        for alert in alerts:

            if alert.source in ['pinpoint', 'rocketmq']:
                name = alert.labels.get('alertname') if alert.labels else None
            elif alert.source == 'custom':
                if alert.labels:
                    if 'alert_title' in alert.labels:
                        name = alert.labels['alert_service'] if 'alert_service' in alert.labels and alert.labels[
                            'alert_service'] != '' else alert.labels['alert_title']
                    elif 'alert_object' in alert.labels:
                        name = alert.labels['alert_object']
                    else:
                        name = alert.summary
                else:
                    name = alert.summary
            elif alert.source == 'tencent_cloud':
                name = alert.labels.get(
                    'policy_name') if alert.labels else None
            else:
                name = None

            if name:
                if alert.source not in items:
                    items[alert.source] = {
                        'source': alert.source,
                        'severities': [],
                        'critical_count': 0,
                        'warning_count': 0,
                        'info_count': 0,
                        'source_count': 0,
                        'items': []
                    }

                if alert.severity == 'critical':
                    items[alert.source]['critical_count'] += 1
                elif alert.severity == 'warning':
                    items[alert.source]['warning_count'] += 1
                elif alert.severity == 'info':
                    items[alert.source]['info_count'] += 1

                items[alert.source]['source_count'] = (
                    items[alert.source]['critical_count'] +
                    items[alert.source]['warning_count'] +
                    items[alert.source]['info_count']
                )

                if alert.severity not in items[alert.source]['severities']:
                    items[alert.source]['severities'].append(alert.severity)

                items[alert.source]['items'].append({
                    'name': name,
                    'severities': [alert.severity],
                    'critical_count': 1 if alert.severity == 'critical' else 0,
                    'warning_count': 1 if alert.severity == 'warning' else 0,
                    'info_count': 1 if alert.severity == 'info' else 0,
                    'account_id': alert.account_id.split('|')[0] if alert.source == 'tencent_cloud' else None
                })

        for source_data in items.values():
            source_data['items'] = aggregate_items(source_data['items'])

            source_data['source_count'] = source_data['source_count']

        result = {
            'items': list(items.values()),
            'statistics': statistics
        }

        return jsonify({
            'status': 'success',
            'data': result
        })
    except Exception as e:
        current_app.logger.error(f'获取健康看板数据失败: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': '服务器内部错误'
        }), 500


@alerts_center_bp.route('/namespace/stats', methods=['GET'])
def get_namespace_stats():
    """根据namespace或product_show_name获取告警聚合统计"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400


        alerts = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).all()


        namespace_product = TencentProductList.query.all()
        namespace_product_dict = {product.namespace: product.product_name for product in namespace_product}


        default_severities = ['critical', 'warning', 'info']

        all_severities = set(default_severities)
        all_severities.update(alert.severity for alert in alerts if alert.severity)
        all_severities = list(all_severities)

        stats = {}

        def init_stat(group_key, group_id, alert):
            return {
                'name': group_key,
                'nameId': group_id if group_id else '',
                'total': 0,
                'source': alert.source if "tencent" in alert.source else '自建监控',
                'sources_items': {},
                'severity_items': {severity: 0 for severity in all_severities}
            }

        for alert in alerts:
            group_key = None
            group_id = None
            if alert.labels:
                if alert.labels.get('namespace'):
                    group_key = namespace_product_dict.get(alert.labels['namespace'], alert.labels['namespace'])
                    group_id = alert.labels['namespace']
                elif alert.labels.get('product_show_name'):
                    group_key = alert.labels['product_show_name']
                    group_id = group_key
            if not group_key:
                group_key = '自建监控'
                group_id = 'custom'

            if group_key not in stats:
                stats[group_key] = init_stat(group_key, group_id, alert)

            stats[group_key]['total'] += 1
            if alert.severity:
                stats[group_key]['severity_items'][alert.severity] = stats[group_key]['severity_items'].get(alert.severity, 0) + 1

            if alert.source == 'tencent_cloud' and alert.account_id:
                source_key = alert.account_id
            else:
                source_key = alert.source if alert.source else 'unknown'
            stats[group_key]['sources_items'][source_key] = stats[group_key]['sources_items'].get(source_key, 0) + 1

        result = [
            {
                'name': data['name'],
                'nameId': data['nameId'],
                'total': data['total'],
                'source': data['source'],
                'sources_items': data['sources_items'],
                'severity_items': data['severity_items'],
                'source_count': len(data['sources_items'])
            }
            for data in stats.values()
        ]

        return jsonify({
            'status': 'success',
            'data': {
                'stats': result,
                'total': len(alerts)
            }
        })

    except Exception as e:
        import traceback
        current_app.logger.error(f"获取namespace统计失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"获取namespace统计失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/namespace/stats', methods=['POST'])
def get_namespace_stats_post():
    """根据namespaceId获取告警聚合统计（POST）"""
    try:
        data = request.get_json(force=True)
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        namespace_id = data.get('namespaceID')

        if not start_time or not end_time or not namespace_id:
            return jsonify({
                'status': 'error',
                'message': 'start_time、end_time、namespaceID为必填参数'
            }), 400

        alerts = []
        if namespace_id == 'custom':
            alerts = MonitoringCenterAlert.query.filter(
                MonitoringCenterAlert.starts_at >= start_time,
                MonitoringCenterAlert.starts_at <= end_time,
                MonitoringCenterAlert.source.in_(['custom', 'pinpoint', 'rocketmq'])
            ).all()
        else:
            try:
                alerts = MonitoringCenterAlert.query.filter(
                    MonitoringCenterAlert.starts_at >= start_time,
                    MonitoringCenterAlert.starts_at <= end_time,
                    func.json_extract(MonitoringCenterAlert.labels, '$.namespace') == namespace_id
                ).all()
            except Exception as e:
                current_app.logger.warning(f"json_extract 查询失败，降级为 like 查询: {str(e)}")
                like_pattern = f'\"namespace\":\"{namespace_id}\"'
                alerts = MonitoringCenterAlert.query.filter(
                    MonitoringCenterAlert.starts_at >= start_time,
                    MonitoringCenterAlert.starts_at <= end_time,
                    MonitoringCenterAlert.labels.like(f'%{like_pattern}%')
                ).all()

        default_severities = ['critical', 'warning', 'info']
        all_severities = set(default_severities)
        all_severities.update(alert.severity for alert in alerts if alert.severity)
        all_severities = list(all_severities)

        stats = {
            'name': namespace_id,
            'nameId': namespace_id,
            'total': len(alerts),
            'source': 'tencent_cloud' if alerts and 'tencent' in alerts[0].source else '自建监控',
            'sources_items': {},
            'severity_items': {severity: 0 for severity in all_severities},
            'source_count': 0
        }

        for alert in alerts:
            if alert.severity:
                stats['severity_items'][alert.severity] = stats['severity_items'].get(alert.severity, 0) + 1
            if alert.source == 'tencent_cloud' and alert.account_id:
                source_key = alert.account_id
            else:
                source_key = alert.source if alert.source else 'unknown'
            stats['sources_items'][source_key] = stats['sources_items'].get(source_key, 0) + 1

        stats['source_count'] = len(stats['sources_items'])

        return jsonify({
            'status': 'success',
            'data': {
                'stats': [stats],
                'total': len(alerts)
            }
        })
    except Exception as e:
        import traceback
        current_app.logger.error(f"POST获取namespace统计失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"POST获取namespace统计失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/panel', methods=['GET'])
def get_alert_panel():
    """获取健康面板数据"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        date = request.args.get('date')

        current_app.logger.info(f"获取健康面板数据: {start_time} - {end_time}, date: {date}")

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400

        # 查询告警数据
        alerts = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).all()

        # 获取产品分类映射
        category_products = TencentCateProduct.query.all()
        product_category_map = {cp.product: cp.categories for cp in category_products}

        # 状态映射函数
        def map_status_to_health(status, severity):
            """将数据库状态映射为前端健康状态"""
            if status == 'OK':
                return 'healthy'
            elif status == 'ALARM':
                if severity == 'critical':
                    return 'critical'
                elif severity == 'warning':
                    return 'warning'
                else:
                    return 'error'
            elif status == 'NO_DATA':
                return 'info'
            elif status == 'NO_CONF':
                return 'warning'
            else:
                return 'info'

        # 解析policy_name的函数
        def parse_policy_name(policy_name):
            """解析3段式policy_name，返回(category, product, instance)"""
            if not policy_name or '-' not in policy_name:
                return None, None, None

            parts = policy_name.split('-')
            if len(parts) >= 3:
                return parts[0], parts[1], parts[2]
            return None, None, None

        # 生成日期范围（最近7天）
        from datetime import datetime, timedelta
        try:
            end_date = datetime.strptime(date, "%Y-%m-%d") if date else datetime.strptime(end_time.split()[0], "%Y-%m-%d")
        except:
            end_date = datetime.now()

        date_range = []
        for i in range(7):
            date_str = (end_date - timedelta(days=i)).strftime("%Y-%m-%d")
            date_range.append(date_str)

        # 数据聚合
        category_stats = {}
        product_stats = {}
        total_statistics = {
            'healthy': 0,
            'info': 0,
            'warning': 0,
            'error': 0,
            'critical': 0,
            'total': 0
        }

        for alert in alerts:
            # 提取policy_name
            policy_name = None
            if alert.labels and isinstance(alert.labels, dict):
                policy_name = alert.labels.get('policy_name')

            if not policy_name:
                continue

            # 解析policy_name
            category, product, instance = parse_policy_name(policy_name)
            if not category or not product:
                continue

            # 获取产品分类
            product_category = product_category_map.get(product)
            if not product_category:
                product_category = category  # 如果没有映射，使用category作为分类

            # 映射健康状态
            health_status = map_status_to_health(alert.status, alert.severity)

            # 获取告警日期
            alert_date = alert.starts_at.strftime("%Y-%m-%d")

            # 统计分类数据
            if product_category not in category_stats:
                category_stats[product_category] = {
                    'healthy': 0,
                    'info': 0,
                    'warning': 0,
                    'error': 0,
                    'critical': 0,
                    'total': 0,
                    'products': set()
                }

            category_stats[product_category][health_status] += 1
            category_stats[product_category]['total'] += 1
            category_stats[product_category]['products'].add(product)

            # 统计产品数据
            product_key = f"{product_category}_{product}"
            if product_key not in product_stats:
                product_stats[product_key] = {
                    'product_name': product,
                    'category': product_category,
                    'healthy': 0,
                    'info': 0,
                    'warning': 0,
                    'error': 0,
                    'critical': 0,
                    'total': 0,
                    'current': {
                        'statuses': set(),
                        'hasData': True,
                        'timestamp': format_datetime(alert.starts_at)
                    },
                    'date_data': {}  # 存储按日期分组的数据
                }

            product_stats[product_key][health_status] += 1
            product_stats[product_key]['total'] += 1
            product_stats[product_key]['current']['statuses'].add(health_status)

            # 按日期存储数据
            date_key = f"date_{alert_date.replace('-', '_')}"
            if date_key not in product_stats[product_key]['date_data']:
                product_stats[product_key]['date_data'][date_key] = {
                    'statuses': set(),
                    'hasData': True,
                    'timestamp': format_datetime(alert.starts_at)
                }
            product_stats[product_key]['date_data'][date_key]['statuses'].add(health_status)

            # 更新总统计
            total_statistics[health_status] += 1
            total_statistics['total'] += 1

        # 构建返回数据
        items = []

        # 按分类分组
        for category in category_stats.keys():
            # 添加分类行
            items.append({
                'id': f"category_{category}",
                'productName': "",
                'category': category,
                'isCategory': True,
                'rss': False,
                'current': {
                    'statuses': [],
                    'hasData': False
                }
            })

            # 添加该分类下的产品
            category_products = [k for k in product_stats.keys() if product_stats[k]['category'] == category]
            for i, product_key in enumerate(sorted(category_products)):
                product = product_stats[product_key]

                # 构建产品项，包含历史日期数据
                product_item = {
                    'id': f"{category}_{i}",
                    'productName': product['product_name'],
                    'category': category,
                    'isCategory': False,
                    'rss': True,
                    'current': {
                        'statuses': list(product['current']['statuses']),
                        'hasData': product['current']['hasData'],
                        'timestamp': product['current']['timestamp']
                    }
                }

                # 添加历史日期数据
                for date_key, date_data in product['date_data'].items():
                    product_item[date_key] = {
                        'statuses': list(date_data['statuses']),
                        'hasData': date_data['hasData'],
                        'timestamp': date_data['timestamp']
                    }

                items.append(product_item)

        return jsonify({
            'status': 'success',
            'message': '获取数据成功',
            'data': {
                'items': items,
                'statistics': total_statistics,
                'dateRange': date_range
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取健康面板数据失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"获取健康面板数据失败: {str(e)}"
        }), 500