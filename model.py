#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   model.py
@Date:    2022/4/12 11:43
@Author:  wanglh
@Desc:    mysql数据库模型
"""
import os
from datetime import datetime

from sqlalchemy import MetaData, create_engine
from sqlalchemy.ext.declarative import declarative_base

import config  # 导入配置模块
from extension import db

env = os.environ.get('FLASK_ENV')
db_url = config.config[env].DB_URI
engine = create_engine(db_url, pool_size=50,
                       max_overflow=50,
                       pool_timeout=60,
                       pool_recycle=3600)
metadata = MetaData()
metadata.bind = engine
metadata.reflect(bind=engine)
Base = declarative_base(metadata=metadata)  # 自动获取 metadata 属性


class EntityBase(object):
    """解决SqlAlchemy查询的时候存在的_sa_instance_state"""

    def to_one_json(self):
        fields = self.__dict__
        if "_sa_instance_state" in fields:
            del fields["_sa_instance_state"]
        return fields

    # 多个query对象
    def double_to_dict(self):
        result = {}
        for key in self.__mapper__.c.keys():
            if getattr(self, key) is not None:
                result[key] = getattr(self, key)
            else:
                result[key] = getattr(self, key)
        return result

    @staticmethod
    def to_all_json(query) -> list:
        v = [i.double_to_dict() for i in query]
        return v


class BaseModel(db.Model):
    """
    实现自动映射获取
    """

    __abstract__ = True

    @classmethod
    def get_time_fields(cls):
        """ 获取时间类型字段 """
        time_fields = [
            field.name for field in cls.__table__.columns
            if isinstance(field.type, (db.DateTime, db.Date, db.Time))
        ]
        return time_fields

    def to_dict_is_time(self, include_basemodel=False):
        from utils import get_time_stamp
        time_fields = self.get_time_fields()
        result_dict = {column.name: getattr(self, column.name) if column.name not in time_fields else get_time_stamp(getattr(self, column.name)) for column in self.__table__.columns}

        if include_basemodel and hasattr(self, 'basemodel'):
            # 假设 basemodel 是通过某种方式附加到实例上的
            result_dict['pre_alias'] = getattr(self.basemodel, 'pre_alias', None)

        return result_dict

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class IpListModel(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ip_list']


class CiSandbox(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_sandbox']


class CiSandboxProjectiles(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_sandbox_project_details']


class CiSandboxOperationlogs(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_sandbox_operation_logs']


class CiWorkflow(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_workflow']


class CiBusinessline(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_business_line']


class CiBusinesslineMembers(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_business_line_members']


class CiProjects(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_projects']
    CHOICE_TYPE_ITEMS = {
        0: "后端",
        1: "前端",
        2: "其他",
        3: "半弹窗"
    }


class CiSandboxDomainBind(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_sandbox_domain_bind']


class SsoApplication(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sso_application']


class SsousersModel(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sso_users']


class SsoroleModel(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sso_role']


class SsoaccessModel(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sso_access']


class SsouserroleaccessModel(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sso_user_role_access']


# TAPD相关表
class CiTapdIterations(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_tapd_iterations']


class CiTapdStories(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_tapd_stories']


class CiCompanyApollo(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ci_company_apollo']


# 日志信息相关表
class OperationLog(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['operation_log']


# SLA相关表

class SlaEvents(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['sla_events']


class SlaEventReport(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['sla_event_report']


# 版本发布相关表
class CiUpgradeSchedule(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_upgrade_schedule']


class CiUpgradeprjDetails(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_upgrade_prj_details']


class CiUpgradeLog(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_upgrade_log']


# DnsPod 相关
class DnsDomain(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['dns_domain']


class DnsRecord(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['dns_record']


class MonServicealarmHistory(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['mon_servicealarm_history']


class MonServiceAlarm(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['mon_service_alarm']


class MonDatasourceRegistration(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['mon_datasource_registration']


# 服务-端口映射关系
class CiSandboxPortBind(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_sandbox_portbind']


class CiSandboxConfigTemplate(Base, EntityBase, BaseModel):
    CHOICE_TYPE_ITEMS = {
        1: "apollo",
        2: "nacos2.0",
        3: "nacos3.0'"
    }
    __table__ = metadata.tables['ci_sandbox_config_template']


class CiOperationFileupload(Base, BaseModel, EntityBase):
    CHOICE_TYPE_ITEMS = {
        0: '永久保存',
        1: '临时保存'
    }
    __table__ = metadata.tables['ci_operation_fileupload']


class CiOperationFileuploadPathlist(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_operation_fileupload_pathlist']


class CiSanboxBindBase(EntityBase):
    id = db.Column(db.BigInteger, nullable=False, primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), default=None)
    AppID = db.Column(db.String(100), default=None)
    secretKey = db.Column(db.String(255), default=None)
    status = db.Column(db.SmallInteger, default=0)
    occupy_sbxid = db.Column(db.String(20), default=None)
    update_time = db.Column(db.DateTime, default=datetime.now)


class CiSandboxMiniAppBind(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_sandbox_miniapp_bind']
    __tablename__ = 'ci_sandbox_miniapp_bind'


class CiSandboxOfficeAccountBind(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_sandbox_officeaccount_bind']


class CiSandboxWechatBind(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_sandbox_wechat_bind']


class CiGrayEvent(Base, EntityBase, BaseModel):
    CHOICE_STATUS_ITEMS = {
        0: "未启用",
        1: "已生效",
        2: "已停止"
    }
    CHOICE_TAG_TYPE_ITEMS = {
        0: "规则模式",
        1: "cookie模式"
    }
    __table__ = metadata.tables['ci_grayevent']


class CiGrayEventRule(Base, EntityBase, BaseModel):
    CHOICE_RULE_TYPE_ITEMS = {
        1: "ip",
        2: "albumid",
        3: "region地域",
        4: "header wego-channel",
        5: "header wego-version"
    }
    __table__ = metadata.tables['ci_grayevent_rule']


class NssVersionCfg(Base, EntityBase, BaseModel):
    CHOICE_ENV_ITEMS = {
        0: "生产",
        1: "预发布1",
        2: "预发布2",
        3: "生产B环境"
    }
    CHOICE_STATUS_ITEMS = {
        0: "待审批",
        1: "已生效",
        2: "已过期"
    }
    __table__ = metadata.tables['nss_version_cfg']


class NssProjectCfg(Base, EntityBase, BaseModel):
    CHOICE_TYPE_ITEMS = {
        "prd": "线上版本",
        "canary": "验收版本",
        "grey": "灰度版本",
        "default": "默认"
    }
    __table__ = metadata.tables['nss_project_cfg']


class NssVersionList(Base, EntityBase, BaseModel):
    CHOICE_ENV_ITEMS = {
        0: "生产",
        1: "预发布1",
        2: "预发布2"
    }
    __table__ = metadata.tables['nss_versionlist']


class NssOperationLog(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['nss_operation_log']


# 降级相关库表信息
class CiPanicUriLevel(Base, EntityBase, db.Model):
    __table__ = metadata.tables['ci_panic_uri_level']


class CiPanicUriList(Base, EntityBase, db.Model):
    CHOICE_STATUS_ITEMS = {
        1: "有效",
        2: "禁用"
    }
    __table__ = metadata.tables['ci_panic_urilist']


class KafkaNginxError(Base, db.Model, EntityBase):
    __table__ = metadata.tables['kafka_nginx_error']


class KafkaErrorCode(Base, db.Model, EntityBase):
    __table__ = metadata.tables['kafka_error_code']


class KafkaErrorCode10m(Base, db.Model, EntityBase):
    __table__ = metadata.tables['kafka_error_code_10m']


class NssBuildInfo(Base, EntityBase, db.Model):
    __table__ = metadata.tables['nss_build_info']


class CiCloudUsers(Base, EntityBase, db.Model):
    __table__ = metadata.tables['ci_cloud_users']


class NssNetWeakTest(Base, EntityBase, db.Model):
    __table__ = metadata.tables['nss_net_weak_test']


# 飞书库
class FeishuApprovalDef(Base, EntityBase, db.Model):
    __table__ = metadata.tables['feishu_approval_def']


class FeishuApprovalNode(Base, EntityBase, db.Model):
    __table__ = metadata.tables['feishu_approval_node']


class FeishuDptData(Base, EntityBase, db.Model):
    __table__ = metadata.tables['feishu_dpt_data']


class FeishuUserData(Base, EntityBase, db.Model):
    __table__ = metadata.tables['feishu_user_data']


class FeishuChatData(Base, EntityBase, db.Model):
    __table__ = metadata.tables['feishu_chat_data']


class NoticeslistModel(BaseModel, EntityBase, Base):
    CHOICE_LEVEL = {
        "info": 0, "WARN": 1, "ERROR": 2, "FATAL": 3
    }
    __table__ = metadata.tables['notices_list']


class WeeklyCostReport(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['weekly_cost_report']


class UserAuthentication(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['user_authentication']


class MqTopicMonitorConfig(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['mq_toplic_monitor_config']


class OvsACDN(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ovs_client_geo_country']


class OvsIpCountry(Base, BaseModel, EntityBase):
    __table__ = metadata.tables['ovs_client_ipbycountry']


class PpPrometheusConfig(Base, EntityBase, BaseModel):
    CHOICE_STATUS_ITEMS = {
        1: "企微",
        2: "飞书"
    }
    __table__ = metadata.tables['pp_prometheus_config']


class PinpointCount(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['pinpoint_count']


class ErrAlertFiringModel(Base, EntityBase, BaseModel):
    __table__ = metadata.tables["err_alert_firing"]


class CiUriwhitelist(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_nginx_uriwhitelist']


class ErrorIgnoreRulesModel(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['err_ignore_rules']


class ErrorAlertRulesModel(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['err_alert_rules']


# waf 安全中心
class WafAttackDetails(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_attack_details']


class WafBaseConfig(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_base_config']

    CHOICE_LEVEL_ANTI = {
        0: "关闭",
        1: "观察",
        2: "验证码",
        3: "拦截"
    }

    CHOICE_LEVEL_FLOW = {
        0: "关闭",
        1: "观察",
        2: "验证码",
        3: "拦截"
    }

    CHOICE_LEVEL_BLACK = {
        0: "关闭",
        1: "开启"
    }


class WafBotIpWhitelist(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_bot_ip_whitelist']


class WafBotRestrictedUris(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_bot_restricted_uris']


class WafBotMonitoredTokens(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_bot_monitored_tokens']


class WafFlowctrlConfig(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_flowctrl_config']
    CHOICE_ACTIVE = {
        0: "关闭",
        1: "开启"
    }


class WafFlowctrlRule(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_flowctrl_rule']


class WafPolicies(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_policies']
    CHOICE_TYPE_ITEMS = {
        0: "低风险",
        1: "中风险",
        2: "高风险"
    }


class WafPolicyRules(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_policy_rules']


class WafSecurityPolicies(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_security_policies']


class WafSourceDetails(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_source_details']


class WafAttackAggregatedByType(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_attack_aggregated_by_type']


class WafAttackAggregatedByIp(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_attack_aggregated_by_ip']


class KafkaEventNotify(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['kafka_event_notify']

    def to_dict(self):
        import base64
        from utils import get_time_stamp
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if column.name == 'details':
                if value:
                    try:
                        value = base64.b64decode(value).decode('utf-8')
                    except Exception as e:
                        value = value
                else:
                    value = None
            elif isinstance(column.type, (db.DateTime, db.Date, db.Time)):
                value = get_time_stamp(value) if value else None
            result[column.name] = value
        return result

    @classmethod
    def get_time_fields(cls):
        """ 获取时间类型字段 """
        time_fields = [
            field.name for field in cls.__table__.columns
            if isinstance(field.type, (db.DateTime, db.Date, db.Time))
        ]
        return time_fields


class CiSqlSlowLogInfo(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_sql_slow_log_info']

    def to_dict(self):
        from datetime import datetime, timedelta
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if column.name == "Timestamp" and value is not None:
                value = datetime.utcfromtimestamp(value) + timedelta(hours=8)
                value = value.strftime('%Y-%m-%d %H:%M:%S')
            result[column.name] = value
        return result


class AssetDatabase(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['asset_database']

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


class BizMoniDataColl(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['biz_moni_data_coll']

    @classmethod
    def get_time_fields(cls):
        """ 获取时间类型字段 """
        time_fields = [
            field.name for field in cls.__table__.columns
            if isinstance(field.type, (db.DateTime, db.Date, db.Time))
        ]
        return time_fields

    def to_dict(self):
        from utils import get_time_stamp
        time_fields = self.get_time_fields()
        return {column.name: getattr(self, column.name) if column.name not in time_fields else get_time_stamp(getattr(self, column.name)) for column in self.__table__.columns}


class BizMoniIdTitle(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['biz_moni_id_title']

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


class CiPreEnvInfo(Base, EntityBase, BaseModel):
    CHOICE_ENV_STATUS = {
        0: "申请中",
        1: "创建中",
        2: "使用中",
        3: "已封存",
        4: "释放",
        5: "创建失败"
    }

    __table__ = metadata.tables['ci_pre_env_info']


class CiPreServiceMap(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_pre_service_map']


class CiPreBuildInfo(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_pre_build_info']


class CiPreGrayRule(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_pre_grayrule']

class CiPreGrayRuleUri(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_pre_grayrule_uri']

class CiPreOpLogs(Base, EntityBase, BaseModel):
    TASK_TYPE_DICT = {
        0: "初始化",
        1: "打包服务",
        2: "删除环境",
        3: "封存环境",
        4: "销毁环境",
        5: "更新环境"
    }

    STEP_RESULT_DICT = {
        0: "成功",
        1: "失败",
        2: "部分成功",
        3: "进行中"
    }
    COLOR_DICT = {
        0: "#419EFF",
        1: "#F56C6D",
        2: "#E6A23D",
        3: "#67C23A"
    }
    __table__ = metadata.tables['ci_pre_op_logs']


class CiPreServiceOpLogs(Base, EntityBase, BaseModel):
    TASK_TYPE_DICT = {
        0: "新增服务",
        1: "构建服务",
        2: "删除服务",
    }

    COLOR_DICT = {
        0: "#419EFF",
        1: "#F56C6D",
        2: "#E6A23D",
        3: "#67C23A"
    }

    STEP_RESULT_DICT = {
        0: "成功",
        1: "失败",
        2: "部分成功",
        3: "进行中"
    }
    __table__ = metadata.tables['ci_pre_service_op_logs']



class CiPreTraffic(Base, EntityBase, BaseModel):
    TASK_TYPE_DICT = {
        1: "用户请求",
        2: "RPC 流入",
        3: "RPC 流出"
    }
    __table__ = metadata.tables['ci_pre_traffic']



class ProdDomainDnsRes(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['prod_domain_dns_res']


class AppLogList(Base, EntityBase, BaseModel):
    TASH_STATUS = {
        0: "解析中",
        1: "解析完成"
    }
    __table__ = metadata.tables['applog_list']


class AppLogDetail(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['applog_details']



class ElkAlertRules(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['elk_alert_rules']


class ElkRuleConditions(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['elk_rule_conditions']


class ElkAlertHistory(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['elk_alert_history']
    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                # 将时间格式化为带时区的字符串，+08:00 表示东八区
                result[column.name] = value.strftime('%Y-%m-%d %H:%M:%S+08:00')
            else:
                result[column.name] = value
        return result

class ApiRequest(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_prod_stat_api_requests']

    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.strftime('%Y-%m-%d %H:%M:%S+08:00')
            else:
                result[column.name] = value
        return result

class ApiRequest1m(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_prod_stat_api_requests_1m']

    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.strftime('%Y-%m-%d %H:%M:%S+08:00')
            else:
                result[column.name] = value
        return result

class ApiRequest1h(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_prod_stat_api_requests_1h']

    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.strftime('%Y-%m-%d %H:%M:%S+08:00')
            else:
                result[column.name] = value
        return result

class CiProdStatApiErrors(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['ci_prod_stat_api_errors']

    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.strftime('%Y-%m-%d %H:%M:%S+08:00')
            else:
                result[column.name] = value
        return result


class MonitoringCenterAlert(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['mon_center_alerts']

class TencentProductList(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['tencent_product_list']

class WafIpBlackList(Base, EntityBase, BaseModel):
    __table__ = metadata.tables['waf_ip_blacklist']

    @classmethod
    def get_time_fields(cls):
        """ 获取时间类型字段 """
        time_fields = [
            field.name for field in cls.__table__.columns
            if isinstance(field.type, (db.DateTime, db.Date, db.Time))
        ]
        return time_fields

    @staticmethod
    def to_all_json(query) -> list:
        """
        将查询结果中的时间类型字段格式化为时间戳
        """
        from utils import get_time_stamp
        result = []
        for item in query:
            item_dict = item.to_dict()
            for key, value in item_dict.items():
                if key in item.get_time_fields() and value is not None:
                    item_dict[key] = get_time_stamp(value)
            result.append(item_dict)
        return result
