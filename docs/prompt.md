请实现 `blueprint/moniterCenter/alerts_center.py` 文件中的 `get_alert_panel` 函数，该函数需要为 HealthPanel 前端组件提供健康状态数据。

**实现要求：**

1. **数据源**：查询 `mon_center_alerts` 表，表结构如下：
```
CREATE TABLE `mon_center_alerts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警来源 (e.g., ''alertmanager'', ''tencent_cloud'', ''alibaba_cloud'', ''custom'')',
  `fingerprint` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警的唯一指纹，用于去重',
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源系统的告警 ID (可选)',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警状态：ALARM(未恢复)/OK(已恢复)/NO_DATA(数据不足)/NO_CONF(已失效)',
  `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '严重等级 (e.g., ''critical'', ''warning'', ''info'') - 需要规范化',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警摘要/标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '告警详细描述',
  `labels` json DEFAULT NULL COMMENT '告警标签 (Key-Value pairs)',
  `annotations` json DEFAULT NULL COMMENT '告警注解 (Key-Value pairs)',
  `starts_at` datetime NOT NULL COMMENT '告警开始时间 (UTC)',
  `ends_at` datetime DEFAULT NULL COMMENT '告警解决时间 (UTC, 可为空)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `acknowledged_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '确认人 (如果实现用户系统)',
  `acknowledged_at` datetime DEFAULT NULL COMMENT '确认时间 (如果实现用户系统)',
  `alarm_status` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '告警状态：ALARM未恢复/OK已恢复/NO_DATA数据不足/NO_CONF已失效',
  `account_id` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账号ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_fingerprint_status` (`fingerprint`,`status`),
  KEY `idx_status` (`status`),
  KEY `idx_severity` (`severity`),
  KEY `idx_starts_at` (`starts_at`),
  KEY `idx_ends_at` (`ends_at`)
) ENGINE=InnoDB AUTO_INCREMENT=26429 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='告警信息表';
```

2. **产品分类逻辑**：
   - 从告警记录的 `labels` JSON 字段中提取 `policy_name` 值
   - 只处理符合 至少 3 段式命名规范的 `policy_name`（使用 '-' 作为分隔符，例如：category-product-instance）
   - 产品名称获取方式：为 `policy_name`根据'-'分隔的第一个元素
   - 使用 `tencent_cate_product` 表进行产品分类映射：

```
CREATE TABLE `tencent_cate_product` (
  `id` int NOT NULL AUTO_INCREMENT,
  `categories` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `product` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniqe_product` (`product`) USING BTREE,
  KEY `index_categories` (`categories`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='腾讯云产品分类列表';
```

3. **接口规范**：
   - 函数应接受时间范围参数（start_time, end_time）
   - 返回 JSON 格式数据，包含按产品分类分组的健康状态统计
   - 支持多种健康状态：ALARM、OK、NO_DATA、NO_CONF
   - 返回格式应与现有接口保持一致（参考 `get_healthboard_data` 和 `get_namespace_stats` 的返回格式）

4. **代码约束**：
   - 只修改 `get_alert_panel` 函数的实现
   - 保持现有代码结构和命名规范
   - 使用现有的数据库模型和查询模式
   - 遵循现有的错误处理和日志记录方式
   - 保持与其他函数相同的响应格式

5. **功能要求**：
   - 按产品分类进行数据聚合
   - 统计各种健康状态的数量
   - 支持时间范围筛选
   - 提供适合前端 HealthPanel 组件使用的数据结构



##