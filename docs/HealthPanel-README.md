# HealthPanel 组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-3.2.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue)

## 组件概述

HealthPanel 是AlertCenter的核心子组件，用于展示系统各个产品模块在指定时间段内的健康状态历史记录。组件采用表格形式展示数据，支持按产品分类分组显示，提供日期导航和产品筛选功能，与AlertCenter通过事件总线进行通信。

### 主要功能
- **健康状态展示**: 以表格形式展示各产品模块的健康状态历史
- **分类分组**: 按产品分类（计算、高性能计算、分布式云、容器）进行分组显示
- **状态统计**: 实时统计5种健康状态的数量分布
- **多状态支持**: 支持产品同时显示多种健康状态（因为产品下的实例可能存在多重状态）
- **时间导航**: 支持按周切换查看不同时间段的健康数据
- **产品筛选**: 支持按产品名称筛选显示特定产品的健康状态
- **图标展示**: 使用SVG图标直观展示不同健康状态

### 技术栈
- **前端框架**: Vue 2.x
- **UI组件库**: Element UI 2.x
- **时间处理**: Day.js
- **样式预处理**: SCSS
- **图标资源**: 本地静态资源

## 组件结构

```mermaid
graph TD
  A[HealthPanel] --> B[状态统计区域]
  A --> C[筛选控制区域]
  A --> D[健康状态表格]
  
  B --> E[5种状态统计]
  C --> F[产品筛选器]
  C --> G[日期选择器]
  C --> H[日期导航按钮]
  
  D --> I[分类行]
  D --> J[产品行]
  D --> K[日期列]
  
  E --> L[正常/提示/警告/严重/紧急]
  I --> M[分类标签]
  J --> N[产品名称]
  J --> O[RSS图标]
  K --> P[多状态图标组]
```

## API 接口规范

### 1. 获取健康面板数据

```http
GET /alertscenter/api/v1/healthpanel
```

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| date | string | 是 | 查询日期 | "2025-07-30" |
| start_time | string | 是 | 开始时间 | "2025-07-30 00:00:00" |
| end_time | string | 是 | 结束时间 | "2025-07-30 23:59:59" |

#### 请求示例

```javascript
const params = {
  date: "2025-07-30",
  start_time: "2025-07-30 00:00:00",
  end_time: "2025-07-30 23:59:59"
}
```

#### 响应数据结构

```typescript
interface HealthPanelResponse {
  status: string;
  message: string;
  data: {
    items: HealthItem[];
    statistics: HealthStatistics;
    dateRange: string[];
  };
}

interface HealthItem {
  id: string;
  productName: string;
  category: string;
  isCategory: boolean;
  rss: boolean;
  current: HealthStatus;
  [dateKey: string]: HealthStatus | any; // 动态日期字段
}

interface HealthStatus {
  statuses: ('healthy' | 'info' | 'warning' | 'error' | 'critical')[];
  hasData: boolean;
  timestamp?: string;
  details?: string;
}

interface HealthStatistics {
  healthy: number;
  info: number;
  warning: number;
  error: number;
  critical: number;
  total: number;
}
```

#### 响应示例

```json
{
  "status": "success",
  "message": "获取数据成功",
  "data": {
    "items": [
      {
        "id": "category_计算",
        "productName": "",
        "category": "计算",
        "isCategory": true,
        "rss": false,
        "current": {
          "statuses": [],
          "hasData": false
        }
      },
      {
        "id": "计算_0",
        "productName": "云服务器",
        "category": "计算",
        "isCategory": false,
        "rss": true,
        "current": {
          "statuses": ["healthy", "warning"],
          "hasData": true,
          "timestamp": "2025-07-30 10:00:00"
        },
        "date_2025_07_29": {
          "statuses": ["error"],
          "hasData": true,
          "timestamp": "2025-07-29 10:00:00"
        },
        "date_2025_07_28": {
          "statuses": ["healthy"],
          "hasData": true,
          "timestamp": "2025-07-28 10:00:00"
        }
      }
    ],
    "statistics": {
      "healthy": 45,
      "info": 3,
      "warning": 8,
      "error": 5,
      "critical": 2,
      "total": 63
    },
    "dateRange": [
      "2025-07-30",
      "2025-07-29",
      "2025-07-28",
      "2025-07-27",
      "2025-07-26",
      "2025-07-25",
      "2025-07-24"
    ]
  }
}
```

## 数据结构定义

### 1. 健康状态枚举

| 状态值 | 中文名称 | 图标来源 | 颜色代码 | 优先级 | 说明 |
|--------|----------|----------|----------|--------|------|
| healthy | 正常 | 本地资源 | #67c23a | 1 | 系统运行正常 |
| info | 提示 | 本地资源 | #409eff | 2 | 一般性提示信息 |
| warning | 警告 | 本地资源 | #e6a23c | 3 | 需要关注的警告 |
| error | 严重 | 本地资源 | #f56c6c | 4 | 严重错误 |
| critical | 紧急 | 本地资源 | #a8071a | 5 | 紧急故障 |